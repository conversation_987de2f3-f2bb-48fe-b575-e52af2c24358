flowchart TD
    %% ===== DATA SOURCES (LIMITED FOR FEASIBILITY) =====
    subgraph SOURCES["🔌 Data Sources (APIs - Free/Low Cost)"]
        CG["CoinGecko API
        📊 OHLCV Data
        Endpoint: /coins/{id}/ohlc
        Freq: Every 15 min
        Cost: Free (rate-limited)"]
        
        REDDIT["Reddit API
        📰 Sentiment via Subreddits
        Endpoint: /r/CryptoCurrency/hot
        Freq: Hourly
        Cost: Free (with limits)"]
        
        ETHSCAN["Etherscan API
        ⛓️ Ethereum Txns & Gas
        Endpoint: /api?module=account&action=txlist
        Freq: Hourly
        Cost: Free tier (limited calls)"]
    end

    %% ===== DATA INGESTION (SIMPLIFIED) =====
    subgraph INGEST["🚀 Data Ingestion & Storage"]
        SCRIPT["Python Scripts
        🐍 requests + pandas
        • Fetch data via HTTP
        • Validate (range, freshness)
        • Cache to avoid rate limits
        • Schedule: cron/GitHub Actions"]
        
        DB["Simple Database
        🗄️ SQLite/Supabase (Free Tier)
        • Store raw data as JSON/CSV
        • Schema: timestamp, type, value
        • Backup: Weekly manual export"]
    end

    %% ===== AGENTIC CORE (MULTIPLE AGENTS WITH OPEN-SOURCE MODELS) =====
    subgraph AGENTS["🤖 Agentic Core (Multi-Agent System via CrewAI)"]
        HUB["Agent Consensus Hub
        🧠 Chroma Vector Store (Open-Source)
        • Shared context (data, past insights)
        • Embeddings: SentenceTransformers
        • Query: Top-3 relevant items
        • Storage: Local (free) or cloud ($5/mo)"]
        
        TSAGENT["Time-Series Agent
        📊 Role: Price Trend Analysis
        Model: Llama-3-8B-Instruct (Hugging Face)
        • Input: OHLCV + indicators
        • Output: Trend forecast + confidence
        • Context: Past trends from Hub
        • Freq: Every 15 min"]
        
        SENTAGENT["Sentiment Agent
        😊 Role: Market Mood Analysis
        Model: Llama-3-8B-Instruct
        • Input: Reddit posts + headlines
        • Output: Sentiment score [-1,1]
        • Context: Recent sentiment from Hub
        • Freq: Hourly"]
        
        CHAINAGENT["On-Chain Agent
        ⛓️ Role: Blockchain Activity
        Model: Llama-3-8B-Instruct
        • Input: Tx volume + gas fees
        • Output: Activity impact + confidence
        • Context: Historical on-chain from Hub
        • Freq: Hourly"]
        
        SYNTHAGENT["Consensus Synthesizer Agent
        📈 Role: Final Forecast Synthesis
        Model: Llama-3-8B-Instruct
        • Input: Outputs from other agents
        • Output: Unified forecast + explanation
        • Logic: Weighted by confidence + history
        • Freq: Every 15 min"]
    end

    %% ===== OUTPUT & DELIVERY =====
    subgraph OUTPUT["📤 Output & Delivery"]
        WEB["Web Dashboard
        🌐 Streamlit on Vercel (Free Tier)
        • Visuals: Charts + forecast summary
        • Unique: Agent Contribution Map (pie chart)
        • Updates: Every 15 min
        • Cost: Free hosting"]
        
        EMAIL["Email Reports
        📧 SendGrid Free Tier
        • Daily forecast + key insights
        • Format: Simple text/HTML
        • Trigger: Cron job daily
        • Cost: Free (100 emails/month)"]
    end

    %% ===== MONITORING & FEEDBACK (BASIC) =====
    subgraph MONITOR["👁️ Monitoring & Feedback"]
        LOGS["Logging
        📝 Local Files/Google Sheets (Free)
        • Log API calls + agent outputs
        • Errors: Timestamp + message
        • Manual review: Weekly (1-2 hrs)
        • Cost: Free"]
        
        FEEDBACK["Feedback Loop
        🔄 User Input + Accuracy Tracking
        • Simple form in dashboard
        • Track forecast vs. actual (manual)
        • Adjust agent weights over time
        • Cost: Free"]
    end

    %% ===== CONNECTIONS =====
    SOURCES --> SCRIPT
    SCRIPT --> DB
    DB --> HUB
    HUB --> TSAGENT
    HUB --> SENTAGENT
    HUB --> CHAINAGENT
    TSAGENT -->|Read/Write Insights| HUB
    SENTAGENT -->|Read/Write Insights| HUB
    CHAINAGENT -->|Read/Write Insights| HUB
    TSAGENT --> SYNTHAGENT
    SENTAGENT --> SYNTHAGENT
    CHAINAGENT --> SYNTHAGENT
    SYNTHAGENT -->|Final Forecast| WEB
    SYNTHAGENT -->|Final Forecast| EMAIL
    SYNTHAGENT -->|Log Results| LOGS
    WEB -->|User Ratings| FEEDBACK
    FEEDBACK -->|Adjust Weights| SYNTHAGENT
    LOGS -->|Error Alerts| EMAIL